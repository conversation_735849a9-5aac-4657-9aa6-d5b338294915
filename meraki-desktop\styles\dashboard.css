body {
  font-family: sans-serif;
  margin: 0;
  padding: 0;
  background: #f9f9f9;
}

.container {
  padding: 20px;
  position: relative;
  height: 95vh;
}

header.stats {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.card {
  flex: 1;
  padding: 10px;
  border-radius: 8px;
  color: #333;
  font-weight: bold;
  background: #eee;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card span {
  display: block;
  font-size: 1.2em;
  margin-top: 5px;
}

.yellow { background: #fff8dc; }
.blue { background: #dbeafe; }
.green { background: #d1fae5; color: #065f46; }
.sky { background: #e0f7fa; }
.pink { background: #ffe4e6; }

.tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.rightTabs {
  position: absolute;
  justify-items: center;
  display: flex;
  
  height: 20px;
  margin-bottom: 10px;
  right: 10px;

}


.tab {
  padding: 8px 12px;
  border: none;
  background: #eee;
  cursor: pointer;
  border-radius: 5px;
}

.tab.active {
  background: #a7f3d0;
}

.filters {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

thead th {
  text-align: left;
  background: #e5e7eb;
  padding: 8px;
}

tbody td {
  padding: 7px;
  border-bottom: 1px solid #ddd;
}

.btn {
  padding: 1px 3px;
  margin: 0 2px;
  cursor: pointer;
}

.play {
  background: #93c5fd;
  border: none;
  border-radius: 1px;
}

.stop {
  background: #f87171;
  border: none;
  border-radius: 1px;
}

footer {
  position: absolute;
  background: blueviolet;
  padding: 5px;
  width: 95%;
  color: white;
  bottom: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  border-radius: 10px;
  
}

a {
   color: black;
  float: left;
  padding: 8px 16px;
  text-decoration: none;
  transition: background-color .3s;
}

a.active {
  background-color: #4CAF50;
  color: white;

}

a:hover:not(.active) {background-color: #ddd;}

    .dropdown {
      position: relative;
      display: inline-block;
      font-family: sans-serif;
    }

    .dropdown-toggle {
      padding: 8px 12px;
      border: 1px solid #ccc;
      background-color: white;
      cursor: pointer;
      min-width: 200px;
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      left: 0;
      width: 100%;
      border: 1px solid #ccc;
      border-top: none;
      background-color: white;
      z-index: 1000;
      display: none;
      max-height: 150px;
      overflow-y: auto;
    }

    .dropdown-menu input {
      width: 100%;
      box-sizing: border-box;
      padding: 6px;
      border: none;
      border-bottom: 1px solid #ccc;
      outline: none;
    }

    .dropdown-item {
      padding: 8px 12px;
      cursor: pointer;
    }

    .dropdown-item:hover,
    .dropdown-item.active {
      background-color: #b3d4fc;
    }
