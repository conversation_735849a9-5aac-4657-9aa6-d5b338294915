<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  
  <title>Today's Tasks</title>
  <style>
    body {
      margin: 0;
      padding: 40px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f6f8fb;
    }

    .container {
      max-width: 500px;
      margin: auto;
      background: white;
      padding: 30px;
      border-radius: 14px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    h2 {
      color: #7c3aed;
      margin-bottom: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    textarea {
      resize: none;
      padding: 14px;
      font-size: 16px;
      border-radius: 8px;
      border: 1px solid #ccc;
      height: 100px;
    }

    button {
      width: 100px;
      align-self: flex-start;
      padding: 10px;
      background-color: #7c3aed;
      color: white;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
    }

    button:hover {
      background-color: #5b21b6;
    }

    ul {
      margin-top: 20px;
      padding-left: 0;
      list-style: none;
    }

    li {
      background: #ede9fe;
      padding: 10px 14px;
      margin-bottom: 10px;
      border-left: 4px solid #7c3aed;
      border-radius: 6px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>📝 Today's Tasks</h2>
    <div  class="form-group">
      <textarea id="taskInput" placeholder="Enter a task..." required></textarea>
      <button id="taskBtn">Add</button>
    </div>
    <ul id="taskList"></ul>
  </div>
  <script src="../renderer_files/today-task.js"></script>

</body>
</html>
