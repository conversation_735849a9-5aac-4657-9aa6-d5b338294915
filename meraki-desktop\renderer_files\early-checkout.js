// const axios = require('axios');

document.addEventListener('DOMContentLoaded', () => {
    const lateInBtn = document.getElementById('earlyOutBtn');
    
    lateInBtn.addEventListener('click', async (e) => {
      
        e.preventDefault();
        const description = document.getElementById('earlyOut').value;
        console.log("Early Check out Here : ",description)
      window.electronAPI.earlyCheckOutFun(description);
    });
});
  

