<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <title>My Tasks</title>
  <link rel="stylesheet" href="../styles/dashboard.css" />
</head>
<body>
  <div class="container">
    <header class="stats">
      <div class="card yellow">TIME AT WORK <span id="timeAtWork">00h 00m</span></div>
      <div class="card blue">PRODUCTIVE TIME <span id="productiveTime">00h 00m</span></div>
      <!-- <div class="card green">EFFICIENCY <span id="efficiency">0.0%</span></div> -->
      <div class="card sky">CLOCK IN <span id="clockIn">09:00 AM</span></div>
      <div class="card pink">CLOCK OUT <span id="clockOut">Online</span></div>
    </header>

    <div class="tabs">
      <button id ="todayBtn" class="tab active">Today</button>
      <button id="overdueBtn" class="tab">Overdue</button>
      <button id="upcomingBtn" class="tab">Upcoming</button>
      <button id="completedBtn" class="tab">Completed</button>
    </div>

    <div class="rightTabs">
      <img src="../assets/reload_btn.png"  alt="reload button"/>
    </div>

    <div class="filters">
      
      <select id="projectDropdown">
  
      </select>
      <select id="statusDropdown">
        <option value="taskStatus">Task Status</option>
        <option value="Completed">Completed</option>
        <option value="To Do">To Do</option>
        <option value="In Progress">In Progress</option>
        <option value="Pause">Pause</option>
        
      </select>
    </div>

    <table>
      <thead>
        <tr>
          <th>Task Name</th>
          <th>Project Name</th>
          <th>Spent Time</th>
          <th>Assigned Time</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>

    <footer id="footer">
      <span id="task-label"></span>
      <span id="green">00:00:00</span>
      <span>00h 00m</span>
      <button id="footer-play" class="btn play">▶</button>
      <button id="footer-stop" class="btn stop">■</button>
    </footer>
  </div>
  <script src="../renderer_files/dashboard.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</body>
</html>
