const mongoose = require("mongoose")
 
const screeShotSchema = new mongoose.Schema(
    {
        imagePath: {
            type: String,
            require: true
        },
        keyCounts: {
            type: Number
        },
        userId:{
            type: mongoose.Schema.Types.ObjectId,
            ref:'user'
        }
    }
)
 
const ScreenShotModel = mongoose.model("screenshot",screeShotSchema)
 
module.exports = ScreenShotModel