// const axios = require('axios');

document.addEventListener('DOMContentLoaded', () => {
    const otherBreakBtn = document.getElementById('otherBreakBtn');
    
    otherBreakBtn.addEventListener('click', async (e) => {
      
        e.preventDefault();
        const description = document.getElementById('otherBreak').value;
        console.log("Other Break : ",description)
      window.electronAPI.otherBreakFun({type:'other',description});
    });
});
  

