<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  
  <title>Login</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f0f2f5;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
    }
    .login-container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      width: 300px;
    }
    .login-container h2 {
      text-align: center;
      margin-bottom: 20px;
      color: rgb(82, 28, 158);
    }
    .login-container input {
      width: 100%;
      padding: 10px;
      margin: 10px 0;
      box-sizing: border-box;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    .login-container button {
      width: 100%;
      padding: 10px;
      background: rgb(82, 28, 158);
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
    }
    .login-container button:hover {
      background: rgb(110, 67, 170);
    }
    .login-container img {
        width: 50px;
        height: 50px;
    }
    #message {
      margin-top: 10px;
      text-align: center;
      color: red;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div style="display: flex; justify-content: center; gap: 10px;">
    <img src="../assets/meraki-logo.png" /> 
    <h2>Xogo Studios</h2>
</div>
    
      <input type="email" id="email" placeholder="Email" required />
      <input type="password" id="password" placeholder="Password" required />
      <button type="submit" id = "loginBtn">Login</button>
 
    <div id="message"></div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

<script src="../renderer_files/login.js"></script>
  
</body>
</html>
