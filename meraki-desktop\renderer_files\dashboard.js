
window.electronAPI.productGetFun((data) => {
  const { products, userID, todayActivity, authToken } = data;

  let paginationCount = 0;
  let currentPage = 1;
  const tasksPerPage = 10;
  let filteredTasks = [];
  let currentTaskBtn = ""
  let statusDropdownSelected = "search"
  let projectDropDownSelected = "taskStatus"
  let taskTimeInterval = null
  
  let count = 0;

  const userTableId = document.getElementById("user-table")

    if (userTableId) {
    while (userTableId.firstChild) {
      userTableId.removeChild(userTableId.firstChild); // ✅ Clears all children
    }
  }

  const userTable = document.querySelector("tbody");
  userTable.id = "user-table"
  const comepleteTaskBtn = document.getElementById("completedBtn");
  const projectDropdown = document.getElementById("projectDropdown")
  projectDropdown.innerHTML = ""
  const projectDropdownOption = document.createElement("option")
  projectDropdownOption.innerHTML = "Search..."
  projectDropdownOption.value = "search"
  projectDropdown.appendChild(projectDropdownOption)
  const statusDropdown = document.getElementById("statusDropdown")
  const footerTask = document.getElementById("task-labe")

  projectDropdown.addEventListener("change",(e) => {
   
    filteredTasks = [];
    console.log("Project Dropdown Called ")
    projectDropDownSelected = e.target.value 
      products.forEach((product) => {

        product.taskArr.forEach((task) => {

          switch(currentTaskBtn) {
            case "today" :  
                            const taskDate = new Date(task.startTime).getDate();
                            const today = new Date().getDate();
                              
                              if(taskDate === today && projectDropDownSelected === product.productName && statusDropdownSelected !== "taskStatus" ? statusDropdownSelected === task.taskStatus : true) {
                                
                                   renderTasksDropdown({task,product},todayTaskBtn)
                                 
                              };
                            
                            break
                            
            case "overdue":   
                             
                              if(projectDropDownSelected === product.productName && new Date(task.startTime).getDate() < new Date().getDate() && task.taskStatus !== "Completed" && statusDropdownSelected !== "taskStatus" ? statusDropdownSelected === task.taskStatus : true) {
                            
                                  renderTasksDropdown({task,product},overdueTaskBtn)
                              };

                              break

            case "upcoming": 
                             if(projectDropDownSelected === product.productName && new Date(task.startDate) > new Date() && statusDropdownSelected !== "taskStatus" ? statusDropdownSelected === task.taskStatus : true){
                              
                                      renderTasksDropdown({task,product},upcomingTaskBtn)
                             }
                          
                             break

            case "completed": if(projectDropDownSelected === product.productName && task.taskStatus === "Completed" &&      statusDropdownSelected !== "taskStatus" ? statusDropdownSelected === task.taskStatus : true) {
                               
                                       renderTasksDropdown({task,product},comepleteTaskBtn)
                              }
                              break
                     
          }
              
        })
          
      })
    
  })

  statusDropdown.addEventListener('change',(e) => {
      filteredTasks = []
      console.log("Status Dropdown Called ")
      statusDropdownSelected = e.target.value
      products.forEach((product) => {
        product.taskArr.forEach((task) => {

          switch(currentTaskBtn) {
            case "today" :  
                            const taskDate = new Date(task.startTime).getDate();
                            const today = new Date().getDate();
                              
                              if(taskDate === today && statusDropdownSelected === task.taskStatus && projectDropDownSelected !== "search" ? projectDropDownSelected === product.productName : true) {
                                
                                   renderTasksDropdown({task,product},todayTaskBtn)
                                 
                              };
                            
                            break
                            
            case "overdue":   
                             
                              if(statusDropdownSelected === task.taskStatus && new Date(task.startTime).getDate() < new Date().getDate() && task.taskStatus !== "Completed" && projectDropDownSelected !== "search" ? projectDropDownSelected === product.productName : true) {
                            
                                  renderTasksDropdown({task,product},overdueTaskBtn)
                              };

                              break

            case "upcoming": 
                             if(statusDropdownSelected === task.taskStatus && new Date(task.startDate) > new Date() && projectDropDownSelected !== "search" ? projectDropDownSelected === product.productName : true){
                              
                                      renderTasksDropdown({task,product},upcomingTaskBtn)
                             }
                          
                             break

            case "completed": if(projectDropDownSelected === product.productName && task.taskStatus === "Completed" &&  projectDropDownSelected !== "search" ? projectDropDownSelected === product.productName : true) {
                               
                                       renderTasksDropdown({task,product},comepleteTaskBtn)
                              }
                              break
                     
          }
              
        })
          
      })
  })

  products.forEach((product) => {

    const projectDropdownOption = document.createElement("option")
        projectDropdownOption.innerHTML = product.productName
        projectDropdownOption.value = product.productName
  
        projectDropdown.appendChild(projectDropdownOption)
  })
  
  const overdueTaskBtn = document.getElementById("overdueBtn");
  const todayTaskBtn = document.getElementById("todayBtn");``
  const upcomingTaskBtn = document.getElementById("upcomingBtn");
  const footer = document.getElementById("footer");
  const clockIn = document.getElementById("clockIn");
  const clockOut = document.getElementById("clockOut");
  const timeAtWork = document.getElementById("timeAtWork");
  const productivity = document.getElementById("productiveTime")

  if (todayActivity) {
    clockIn.innerHTML = timeFormatter(todayActivity.checkInTime);
    clockOut.innerHTML = todayActivity.checkOutTime
      ? timeFormatter(todayActivity.checkOutTime)
      : "Online";
    timeAtWork.innerHTML = totalWorkingTimeCalculationFun(todayActivity.totalWorkingTime);
    let productivityFilled = 0

    
    if(todayActivity.totalWorkingTime !== 0) {

      for(let i=0; i<todayActivity.productivityHistory.length; i++) {
          productivityFilled += todayActivity.productivityHistory[i].productivityFilled
        }
        console.log("Productivity Inner Html ",(productivityFilled/todayActivity.totalWorkingTime) * 100)
       productivity.innerHTML = `${((productivityFilled/todayActivity.totalWorkingTime) * 100).toFixed(2)} %`
    } else {
         productivity.innerHTML = "0.0%"
    }
  }

  function timeFormatter(data) {
    const date = new Date(data);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHour = hours % 12 || 12;
    const formattedMinute = minutes.toString().padStart(2, "0");
    return `${formattedHour}:${formattedMinute} ${ampm}`;
  }

  function totalWorkingTimeCalculationFun(totalSeconds) {
      const hours = Math.floor(totalSeconds / 3600);
      const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
      return `${hours}h ${remainingMinutes}m`;
  }

  function totalTimeCalculationFun(totalSeconds) {
  const hours = Math.floor(totalSeconds / 3600);
  const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60)
  return `${hours}h:${remainingMinutes}m:${seconds}s`;
}

  function resetTabs(activeTab) {
    todayTaskBtn.className = "tab";
    comepleteTaskBtn.className = "tab";
    overdueTaskBtn.className = "tab";
    upcomingTaskBtn.className = "tab";
    activeTab.className = "tab active";
  }

  function createPagination() {
    const existingPagination = document.getElementById("pagination");
    if (existingPagination) existingPagination.remove();

    const paginationDiv = document.createElement("div");
    paginationDiv.id = "pagination";
    paginationDiv.style.textAlign = "center";
    paginationDiv.style.margin = "20px 0";

    const paginationElement = document.createElement("div");
    paginationElement.style.display = "inline-block";
    paginationDiv.appendChild(paginationElement);

    paginationCount = Math.ceil(filteredTasks.length / tasksPerPage);

    const leftA = document.createElement("a");
    leftA.innerHTML = "&laquo;";
    leftA.style.cursor = "pointer";
    leftA.addEventListener("click", () => {
      if (currentPage > 1) {
        currentPage--;
        renderPage(currentPage);
        createPagination();
      }
    });
    paginationElement.appendChild(leftA);

    for (let i = 1; i <= paginationCount; i++) {
      const paginationBtn = document.createElement("a");
      paginationBtn.textContent = i;
      paginationBtn.style.margin = "0 5px";
      paginationBtn.style.cursor = "pointer";
      if (i === currentPage) paginationBtn.style.fontWeight = "bold";

      paginationBtn.addEventListener("click", () => {
        currentPage = i;
        renderPage(currentPage);
        createPagination();
      });
      paginationElement.appendChild(paginationBtn);
    }

    const rightA = document.createElement("a");
    rightA.innerHTML = "&raquo;";
    rightA.style.cursor = "pointer";
    rightA.addEventListener("click", () => {
      if (currentPage < paginationCount) {
        currentPage++;
        renderPage(currentPage);
        createPagination();
      }
    });
    paginationElement.appendChild(rightA);

    footer.parentNode.insertBefore(paginationDiv, footer);
  }

  async function renderPage(pageNumber) {
    userTable.innerHTML = "";
    const start = (pageNumber - 1) * tasksPerPage;
    const end = pageNumber * tasksPerPage;
    const paginatedTasks = filteredTasks.slice(start, end);

    for (const { task, product } of paginatedTasks) {
      const row = document.createElement("tr");
      const runningTask =  JSON.parse(localStorage.getItem("runningTask"));

      console.log("Task Status ",task.taskStatus,task.totalSpent)
      const isRunning = runningTask?.taskId === task._id;
      const isTaskRunning = isRunning && runningTask.status === "running";

      if ((task.taskStatus || "").trim().toLowerCase() !== "completed") {
       
        row.innerHTML = `
          <td>${task.taskTitle}</td>
          <td>${product.productName}</td>
          <td>${runningTask?.taskId === task._id ? totalTimeCalculationFun(runningTask.elapsedTime) : totalTimeCalculationFun(task.totalSpent)}</td>
          <td>${task.totalHours}</td>
          <td>
              <img src="${isRunning ? '../assets/pause.png' : '../assets/Play.png'}"
                  class="${isTaskRunning ? 'pause-btn' : 'play-btn'}"
                  data-taskid="${task.taskId}"
                  alt="${task.isStarted ? 'Pause' : 'Play'}"
                  width="26" />
              <img src='../assets/Stop.png'
                  class='submit-btn'
                  data-taskid='${task.taskId}'
                  alt='Submit'
                  width='26' />
          </td>
        `;

        const playBtn = row.querySelector(isTaskRunning ? ".pause-btn" : ".play-btn");
        const stopBtn = row.querySelector(".submit-btn");

        playBtn.addEventListener("click", (e) => taskControllerFun({task, product}, e));
        stopBtn.addEventListener("click", (e) => taskControllerFun({task, product}, e));
      } else {
        row.innerHTML = `
          <td>${task.taskTitle}</td>
          <td>${product.productName}</td>
          <td>${task.totalSpent}</td>
          <td>${task.totalHours}</td>
          <td>
            <img src="../assets/Right.png"
                class="submit-btn"
                data-taskid="${task.taskId}"
                alt="Completed"
                width="26" />
          </td>
        `;
        const stopBtn = row.querySelector(".submit-btn");
        stopBtn.addEventListener("click", (e) => taskControllerFun({task, product}, e));
      }

      userTable.appendChild(row);
    }
  } 

  function renderTasks(filterFn, activeTabBtn) {
    resetTabs(activeTabBtn);
    filteredTasks = [];

    products.forEach((product) => {
      if (product.members.includes(userID)) {
        product.taskArr.forEach((task) => {
          if (filterFn(task)) {
            filteredTasks.push({ task, product });
          }
        });
      }

    });
    console.log(" After Final ",filteredTasks)

    dataCount = filteredTasks.length;
    currentPage = 1;

    renderPage(currentPage);
    createPagination();
  }

  function renderTasksDropdown(data,activeTabBtn) {
        resetTabs(activeTabBtn);
        
        const {task,product} = data
        
        filteredTasks.push({ task, product });
           dataCount = filteredTasks.length;
       currentPage = 1;

    renderPage(currentPage);
    createPagination();
  }

  function timerForTask() {
         taskTimeInterval = setInterval(() => {
          const data = JSON.parse(localStorage.getItem("runningTask")) || {};
          count++; // count is global

          const updatedData = {
            ...data,
            elapsedTime: count,
          };


          localStorage.setItem("runningTask", JSON.stringify(updatedData));
          renderPage(currentPage);
        }, 1000);

        return () => clearInterval(taskTimeInterval);
  }

  async function taskControllerFun(data, ele) {
    const {product, task} = data
    console.log("Task Start Data : ",product._id,task._id)
    const className = ele.target.className;
    let endpoint = "";

    switch (className) {
      case "play-btn":
        endpoint = "start-task";
        ele.target.className = "pause-btn";
        ele.target.src = "../assets/pause.png";
        if(JSON.parse(localStorage.getItem("runningTask"))) {
          
            const previousTask = JSON.parse(localStorage.getItem("runningTask"))
              localStorage.clear()
                try {
              const response = await axios.patch(
                `http://localhost:10000/api/product/pause-task/${previousTask.projectId}/${previousTask.taskId}`,
                {},
                {
                  headers: {
                    Authorization: `Bearer ${authToken}`,
                    "Content-Type": "application/json",
                  },
                }
              );
              console.log(`Response from ${endpoint}:`, response.data);
            
            } catch (e) {
              console.log(" Paused Task Error ",e)
            }
        }
        try {
          const response = await axios.patch(
            `http://localhost:10000/api/product/${endpoint}/${product._id}/${task._id}`,
            {},
            {
              headers: {
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
              },
            }
          );
          console.log(`Response from ${endpoint}:`, response.data);
          
          let taskObj = {
            taskId: task._id,
            projectId: product._id,
            status:"running",
            elapsedTime: task.totalSpent !== 0 ? task.totalSpent : 0
          }
          console.log("Local Task Data going to store ",taskObj.elapsedTime,task.totalSpent)
          localStorage.setItem('runningTask',JSON.stringify(taskObj));
          count = taskObj.elapsedTime
          timerForTask()
          
        } catch (err) {
          console.error(`Error during ${endpoint}:`, err.message);
        }
        // renderPage(currentPage)
        break;

      case "pause-btn":
        endpoint = "pause-task";
        ele.target.className = "play-btn";
        ele.target.src = "../assets/Play.png";
         try {
          const response = await axios.patch(
            `http://localhost:10000/api/product/${endpoint}/${product._id}/${task._id}`,
            {},
            {
              headers: {
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
              },
            }
          );

          console.log("response from pause-btn" ,response)
       if(response.status === 200) {

        setTimeout(() => {
             localStorage.clear()
        window.electronAPI.getUpdatedTasks();
        },500)
        

        clearInterval(taskTimeInterval);
       }
             console.log(`Response from pause : ${products[0].taskArr[0].totalSpent}`);
        } catch (err) {
          console.error(`Error during ${endpoint}:`, err.message);
        }
        break;

      case "submit-btn":
        endpoint = "stop-task";
        ele.target.className = "success-btn";
        ele.target.src = "../assets/Right.png";
        try {
          const response = await axios.patch(
            `http://localhost:10000/api/product/${endpoint}/${product._id}/${task._id}`,
            { },
            {
              headers: {
                Authorization: `Bearer ${authToken}`,
                "Content-Type": "application/json",
              },
            }
          );
          console.log(`Response from ${endpoint}:`, response.data);
          localStorage.clear()
          window.electronAPI.getUpdatedTasks();
          renderPage(currentPage)

        } catch (err) {
          console.error(`Error during ${endpoint}:`, err.message);
        }
        break;

      default:
        return;
    }
  }

  todayTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search"
    statusDropdown.value = "taskStatus"
    currentTaskBtn = "today"
    renderTasks((task) => {
      const taskDate = new Date(task.createdAt).getDate();
      const today = new Date().getDate();
      
      return taskDate === today;
    }, todayTaskBtn);
  });

  overdueTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search"
    statusDropdown.value = "taskStatus"
    currentTaskBtn = "overdue"
    renderTasks((task) => {
      return (
        new Date(task.createdAt).getDate() < new Date().getDate() && task.taskStatus !== "Completed"
      );
    }, overdueTaskBtn);
  });

  upcomingTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search"
    statusDropdown.value = "taskStatus"
    currentTaskBtn = "upcoming"
    renderTasks((task) => new Date(task.startDate) > new Date(), upcomingTaskBtn);
  });

  comepleteTaskBtn.addEventListener("click", () => {
    projectDropdown.value = "search"
    statusDropdown.value = "taskStatus"
    currentTaskBtn = "completed"
    renderTasks((task) => task.taskStatus === "Completed", comepleteTaskBtn);
  });

  todayTaskBtn.click(); // Load default
});

window.electronAPI.clearTaskDataFun(() => {
    console.log("Clearing the Local Data of task ")
    localStorage.clear()
})
