const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  loginSuccess: () => ipcRenderer.send('login-success'),
  getUpdatedTasks: () => ipcRenderer.send("get-updated-tasks"),
  productGetFun: (callback) => ipcRenderer.on("get-product",(_event, data) => callback(data)),
  clearTaskDataFun: (callback) => ipcRenderer.on("clear-task",() => callback()),
  getStoreValue: (key) => ipcRenderer.invoke('store:get', key),
  setStoreValue: (key, value) => ipcRenderer.invoke('store:set', key, value),
});
