// const axios = require('axios');

document.addEventListener('DOMContentLoaded', () => {
    const comepletedTaskBtn = document.getElementById('completedTaskBtn');
    
    comepletedTaskBtn.addEventListener('click', async (e) => {
      
        e.preventDefault();
        const description = document.getElementById('comepletedTask').value;
        console.log("Completed Task Here ",description)
       window.electronAPI.completedTaskFun(description);
    });
});
  

