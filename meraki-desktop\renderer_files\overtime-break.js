
document.addEventListener('DOMContentLoaded', () => {
    const lateInBtn = document.getElementById('overTimeBreakBtn');
    
    lateInBtn.addEventListener('click', async (e) => {
      
        e.preventDefault();
        const description = document.getElementById('overTimeBreak').value;
        console.log("Over Time Break : ",description)
      window.electronAPI.overTimeBreakFun(description);
    });
});
  

